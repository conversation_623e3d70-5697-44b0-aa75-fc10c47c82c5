import { create } from 'zustand';
import { apiService } from '../services/apiService';

// Define User type locally since it's not exported from apiService
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  phoneNumber: string;
  role: 'customer' | 'supplier';
  isEmailVerified: boolean;
  address?: string;
  city?: string;
  country?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  isActive: boolean;
  notifications?: boolean;
  location?: [number, number];
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;

  // Business Information (for suppliers)
  supplierId?: string;
  storeName?: string;
  businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  openHours?: string;
}

type UserStore = {
    user: User | null;
    isLoading: boolean;
    error: string | null;
    setCurrentUser: (user: User | null) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    clearUser: () => void;
    loadUserProfile: () => Promise<void>;
    initializeUser: () => Promise<void>;
    autoInitialize: () => Promise<void>;
}

let isInitialized = false;

export const useCurrentUserData = create<UserStore>((set, get) => ({
    user: null,
    isLoading: false,
    error: null,

    setCurrentUser: (user) => {
        console.log('🔄 Setting current user:', user ? `${user.firstName} ${user.lastName} (${user.email})` : 'null');
        set({ user, error: null });
    },
    setLoading: (isLoading) => set({ isLoading }),
    setError: (error) => set({ error }),
    clearUser: () => {
        console.log('🔄 Clearing user data');
        set({ user: null, error: null });
    },

    loadUserProfile: async () => {
        console.log('🔄 Loading user profile...');
        const isAuth = await apiService.isAuthenticated();
        console.log('🔐 Authentication check result:', isAuth);

        if (!isAuth) {
            console.log('❌ User not authenticated, clearing user data');
            set({ user: null, error: null });
            return;
        }

        set({ isLoading: true, error: null });

        try {
            const response = await apiService.getCurrentUser();
            console.log('📥 Profile response:', response);

            if (response.success && response.data) {
                console.log('✅ Profile loaded successfully:', response.data.user);
                set({ user: response.data.user, isLoading: false, error: null });
            } else {
                console.log('❌ Profile load failed:', response.message);
                set({ user: null, isLoading: false, error: response.message });
            }
        } catch (error) {
            console.log('❌ Profile load error:', error);
            set({
                user: null,
                isLoading: false,
                error: error instanceof Error ? error.message : 'Failed to load profile'
            });
        }
    },

    // Initialize user data on app start
    initializeUser: async () => {
        if (isInitialized) {
            console.log('🔄 User already initialized, skipping...');
            return;
        }

        console.log('🚀 Initializing user data...');
        isInitialized = true;

        const isAuth = await apiService.isAuthenticated();
        console.log('🔐 Initial authentication check:', isAuth);

        if (isAuth) {
            // Try to get user profile
            await get().loadUserProfile();
        } else {
            console.log('❌ No valid authentication found');
            set({ user: null, error: null });
        }
    },

    // Auto-initialize when store is accessed
    autoInitialize: async () => {
        if (!isInitialized && !get().user) {
            console.log('🔄 Auto-initializing user data...');
            await get().initializeUser();
        }
    },
}));